import { useState, useRef } from "react";
import mapboxgl from "mapbox-gl";
import Header from "./Header";
import MapView from "./MapView";
import ControlBox from "./ControlBox";
import SavedFairway from "./SavedFairway";
import SavedWalkingPath from "./SavedWalkingPath";
import { saveFairway, saveWalkingPath } from "../utils/localStorage";
import { saveCourseToSupabase } from "../services/supabaseService";

// Calculate distance between two coordinates using Haversine formula
const calculateDistance = (coord1, coord2) => {
  const [lng1, lat1] = coord1;
  const [lng2, lat2] = coord2;

  const R = 6371e3; // Earth's radius in meters
  const φ1 = (lat1 * Math.PI) / 180;
  const φ2 = (lat2 * Math.PI) / 180;
  const Δφ = ((lat2 - lat1) * Math.PI) / 180;
  const Δλ = ((lng2 - lng1) * Math.PI) / 180;

  const a =
    Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
    Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  return R * c; // Distance in meters
};

const Home = () => {
  // State management for map interactions
  const [currentMode, setCurrentMode] = useState("idle"); // idle, selecting-start, selecting-end, fairway-complete, fairway-saved, creating-walking-path, editing-curve
  const [statusMessage, setStatusMessage] = useState(
    "Select start point to begin Fairway 1"
  );

  // New state for tracking workflow
  const [isFairwaySaved, setIsFairwaySaved] = useState(false);
  const [walkingPathStartPoint, setWalkingPathStartPoint] = useState(null);
  const [isEditingCurve, setIsEditingCurve] = useState(false);
  // const [isRotatingStartPoint, setIsRotatingStartPoint] = useState(false);
  // const [rotationMode, setRotationMode] = useState(null); // 'clockwise' or 'anticlockwise'

  // Points and paths (current fairway being created)
  const [startPoint, setStartPoint] = useState(null); // [lng, lat]
  const [startPointRotation, setStartPointRotation] = useState(0); // rotation angle in degrees
  const [endPoint, setEndPoint] = useState(null); // [lng, lat]
  const [fairwayLine, setFairwayLine] = useState(null); // [[lng, lat], [lng, lat]]
  const [previousFairwayLine, setPreviousFairwayLine] = useState(null); // For undo functionality
  const [walkingPath, setWalkingPath] = useState(null); // [[lng, lat], [lng, lat]]

  // All completed fairways and paths (to keep them visible on map)
  const [completedFairways, setCompletedFairways] = useState([]); // Array of fairway objects
  const [completedWalkingPaths, setCompletedWalkingPaths] = useState([]); // Array of walking path objects

  // Current hole info
  const [currentHole, setCurrentHole] = useState(1);
  const [fairwayCount, setFairwayCount] = useState(0);

  // Popup state
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [isWalkingPathPopupOpen, setIsWalkingPathPopupOpen] = useState(false);

  // Point editing popup state
  const [isPointEditPopupOpen, setIsPointEditPopupOpen] = useState(false);
  const [editingPointType, setEditingPointType] = useState(null); // 'start' or 'end'
  const [editingPointPosition, setEditingPointPosition] = useState(null);

  // Curve editing state
  const [curveControlPoints, setCurveControlPoints] = useState([]); // Array of control points for curve
  const [isDraggingCurvePoint, setIsDraggingCurvePoint] = useState(false);
  const [draggingPointIndex, setDraggingPointIndex] = useState(null);
  const [curveControlPoint, setCurveControlPoint] = useState(null); // The movable control point for the curve
  const [originalStraightLine, setOriginalStraightLine] = useState(null); // Store original straight line

  // Map reference to interact with MapView
  const mapRef = useRef(null);

  // Using API service functions

  // Helper function to create two-segment angular curve (triangle shape)
  const createCurvedLine = (start, control, end) => {
    // Create a two-segment line: start -> control -> end
    // This creates an angular "curve" with two straight line segments
    return [start, control, end];
  };

  // Helper function to calculate rotation angle from two points
  const calculateRotationAngle = (point1, point2) => {
    const dx = point2[0] - point1[0];
    const dy = point2[1] - point1[1];
    // Add 90 degrees to make rectangle perpendicular to the line
    return Math.atan2(dy, dx) * (180 / Math.PI) + 90;
  };

  // Event handlers
  const handleStartPoint = () => {
    setCurrentMode("selecting-start");
    setStatusMessage("Click on map to place start point.");
  };

  const handleContinueFairway = () => {
    setCurrentMode("selecting-end");
    setStatusMessage("Select end point to complete the fairway");
  };

  const handleEndPoint = () => {
    if (!startPoint) return;
    setCurrentMode("selecting-end");
    setStatusMessage("Tap on map to set end point");
  };

  const handleSaveFairway = async () => {
    if (!fairwayLine || !startPoint || !endPoint) return;

    try {
      const distance = calculateDistance(startPoint, endPoint);

      // Calculate rotation angle for the start point
      const rotationAngle = calculateRotationAngle(startPoint, endPoint);

      const fairwayData = {
        holeNumber: currentHole,
        teeLocation: {
          type: "Point",
          coordinates: startPoint,
        },
        basketLocation: {
          type: "Point",
          coordinates: endPoint,
        },
        fairwayPath: {
          type: "LineString",
          coordinates: fairwayLine,
        },
        length: distance,
        createdAt: new Date().toISOString(),
      };

      const success = saveFairway(fairwayData);

      if (success) {
        // Add fairway to completed arrays for map display
        setCompletedFairways((prev) => [
          ...prev,
          {
            holeNumber: currentHole,
            startPoint: startPoint,
            endPoint: endPoint,
            fairwayLine: fairwayLine,
            startPointRotation: rotationAngle, // Store the calculated rotation
            createdAt: new Date().toISOString(),
          },
        ]);

        // Increment fairway count
        setFairwayCount((prev) => prev + 1);

        // Set fairway as saved and prepare for walking path
        setIsFairwaySaved(true);
        setWalkingPathStartPoint(endPoint); // Walking path starts from fairway endpoint
        setCurrentMode("fairway-saved");
        setStatusMessage(
          "Fairway saved! Now you can create walking path, add curve, or end fairway to start new one."
        );

        // Don't reset anything - keep fairway visible and wait for walking path creation
      } else {
        setStatusMessage("Error saving fairway. Please try again.");
      }
    } catch (error) {
      setStatusMessage("Error saving fairway. Please try again.");
    }
  };

  const handleEndFairway = () => {
    // Reset everything and start new fairway cycle
    setStartPoint(null);
    setStartPointRotation(0);
    setEndPoint(null);
    setFairwayLine(null);
    setWalkingPath(null);
    setWalkingPathStartPoint(null);
    setIsFairwaySaved(false);
    setCurveControlPoints([]);
    setCurveControlPoint(null);
    setOriginalStraightLine(null);
    setCurrentHole((prev) => prev + 1);
    setCurrentMode("idle");
    setStatusMessage(`Select start point to begin Fairway ${currentHole + 1}`);
  };

  const handleCreateWalkingPath = () => {
    if (currentMode !== "fairway-saved") return;
    setCurrentMode("creating-walking-path");
    setStatusMessage(
      "Place Walking Path Endpoint (start point is automatically set from fairway endpoint)."
    );
  };

  const handleSaveWalkingPath = async () => {
    if (!walkingPath || !walkingPathStartPoint) return;

    try {
      const distance = calculateDistance(walkingPathStartPoint, walkingPath[1]);

      const walkingPathData = {
        fromHole: currentHole,
        toHole: currentHole + 1,
        pathCoordinates: walkingPath,
        length: distance,
        createdAt: new Date().toISOString(),
      };

      const walkingPathSuccess = saveWalkingPath(walkingPathData);

      if (walkingPathSuccess) {
        // Add to completed walking paths for map display
        setCompletedWalkingPaths((prev) => [
          ...prev,
          {
            fromHole: currentHole,
            toHole: currentHole + 1,
            walkingPath: walkingPath,
            length: distance,
            createdAt: new Date().toISOString(),
          },
        ]);

        setStatusMessage("Walking path saved! Starting next fairway...");

        // Start new fairway cycle automatically
        setTimeout(() => {
          // Set new start point at walking path endpoint
          setStartPoint(walkingPath[1]);
          setStartPointRotation(0); // Will be calculated when end point is placed
          setEndPoint(null);
          setFairwayLine(null);
          setWalkingPath(null);
          setWalkingPathStartPoint(null);
          setIsFairwaySaved(false);
          setCurrentHole((prev) => prev + 1);
          setCurrentMode("start-placed");
          setStatusMessage(
            `Fairway ${currentHole} completed! New start point placed at walking path end. Click 'Continue Fairway' to select end point.`
          );
        }, 2000);
      } else {
        setStatusMessage("Error saving walking path. Please try again.");
      }
    } catch (error) {
      setStatusMessage("Error saving walking path. Please try again.");
    }
  };

  const handleAddCurve = () => {
    if (!startPoint || !endPoint || !fairwayLine) return;

    // Store the current straight line for potential restoration
    setPreviousFairwayLine(fairwayLine);

    // Set mode to selecting curve direction - NO curve created yet
    setCurrentMode("selecting-curve-direction");
    setStatusMessage(
      "Click on the left or right side of the fairway line to create curve."
    );
  };

  // Function to create curve on a specific side (true = right, false = left)
  const createCurveOnSide = (curveToRight) => {
    if (!startPoint || !endPoint || !fairwayLine) return;

    // Store the original straight line for potential restoration
    setOriginalStraightLine([...fairwayLine]);

    // Calculate the midpoint between start and end
    const midPoint = [
      startPoint[0] + (endPoint[0] - startPoint[0]) * 0.5,
      startPoint[1] + (endPoint[1] - startPoint[1]) * 0.5,
    ];

    // Calculate perpendicular offset for the deviation point
    const dx = endPoint[0] - startPoint[0];
    const dy = endPoint[1] - startPoint[1];

    // Create perpendicular vector (rotate 90 degrees)
    let perpX = -dy;
    let perpY = dx;

    // If we want to curve to the left, flip the perpendicular vector
    if (!curveToRight) {
      perpX = -perpX;
      perpY = -perpY;
    }

    // Normalize the perpendicular vector
    const length = Math.sqrt(perpX * perpX + perpY * perpY);
    const normalizedPerpX = perpX / length;
    const normalizedPerpY = perpY / length;

    // Set deviation distance - much smaller and proportional to the line length
    // This creates a subtle curve that's closer to the original line
    const deviationDistance = 0.002;

    // Start with initial deviation point at midpoint
    let deviationPoint = [
      midPoint[0] + normalizedPerpX * deviationDistance,
      midPoint[1] + normalizedPerpY * deviationDistance,
    ];

    // Iteratively adjust the deviation point to achieve equal segment distances
    // This ensures both segments (start->control and control->end) have equal length
    for (let i = 0; i < 20; i++) {
      const dist1 = calculateDistance(startPoint, deviationPoint);
      const dist2 = calculateDistance(deviationPoint, endPoint);
      const totalDist = dist1 + dist2;
      const targetDist = totalDist / 2;

      // If distances are already close enough, break
      if (Math.abs(dist1 - dist2) < 0.5) break;

      // Adjust the position along the original line to balance the distances
      const adjustment = ((dist1 - targetDist) / totalDist) * 0.05;
      const adjustedMidPoint = [
        midPoint[0] - (endPoint[0] - startPoint[0]) * adjustment,
        midPoint[1] - (endPoint[1] - startPoint[1]) * adjustment,
      ];

      deviationPoint = [
        adjustedMidPoint[0] + normalizedPerpX * deviationDistance,
        adjustedMidPoint[1] + normalizedPerpY * deviationDistance,
      ];
    }

    // Create the two-segment angular curve - but DON'T save yet
    const angularCurve = createCurvedLine(startPoint, deviationPoint, endPoint);
    setFairwayLine(angularCurve);

    // Set the movable control point
    setCurveControlPoint(deviationPoint);

    // Set mode to editing curve (to show Save/Cancel buttons)
    setCurrentMode("editing-curve");
    setStatusMessage(
      `Curve created on ${
        curveToRight ? "right" : "left"
      } side. Drag the center circle to adjust curve. Save or Cancel?`
    );

    // No control points needed - curve is non-interactive
    setCurveControlPoints([]);

    // Zoom to the fairway line
    if (mapRef.current && mapRef.current.fitBounds) {
      const bounds = new mapboxgl.LngLatBounds();
      bounds.extend(startPoint);
      bounds.extend(endPoint);
      bounds.extend(deviationPoint); // Include deviation point in bounds
      mapRef.current.fitBounds(bounds, {
        padding: 100,
        maxZoom: 16,
      });
    }
  };

  // Function to create curve based on click position
  const createCurveFromClick = (clickPosition) => {
    if (!startPoint || !endPoint || !fairwayLine) return;

    // Calculate which side of the line the click was on
    const clickPoint = clickPosition;

    // Use cross product to determine which side of the line the click is on
    const dx = endPoint[0] - startPoint[0];
    const dy = endPoint[1] - startPoint[1];
    const px = clickPoint[0] - startPoint[0];
    const py = clickPoint[1] - startPoint[1];

    // Cross product: if positive, point is on left side; if negative, on right side
    const crossProduct = dx * py - dy * px;
    const curveToRight = crossProduct > 0; // INVERTED: Positive (left click) creates right curve

    // Create curve on the clicked side (this will set mode to "editing-curve")
    createCurveOnSide(curveToRight);
  };

  // Function to save the curve and replace the straight line
  const handleSaveCurve = async () => {
    if (!fairwayLine || !startPoint || !endPoint) return;

    try {
      // Save the curved fairway to replace the straight line
      saveCurvedFairwayToStorage(fairwayLine);

      // Go back to fairway-saved state
      setCurrentMode("fairway-saved");
      setStatusMessage(
        "Curve saved! Now you can create walking path, add curve, or end fairway to start new one."
      );
    } catch (error) {
      setStatusMessage("Error saving curve. Please try again.");
    }
  };

  // Function to cancel curve creation and restore straight line
  const handleCancelCurve = () => {
    // Restore the original straight line
    if (originalStraightLine) {
      setFairwayLine(originalStraightLine);
      setOriginalStraightLine(null);
    } else if (previousFairwayLine) {
      setFairwayLine(previousFairwayLine);
      setPreviousFairwayLine(null);
    }

    // Reset curve control point
    setCurveControlPoint(null);

    // Go back to fairway-saved state
    setCurrentMode("fairway-saved");
    setStatusMessage(
      "Curve cancelled. Now you can create walking path, add curve, or end fairway to start new one."
    );
  };

  // Function to save curved fairway and replace the straight line
  const saveCurvedFairwayToStorage = async (curvedLine) => {
    if (!curvedLine || !startPoint || !endPoint) return;

    try {
      const distance = calculateDistance(startPoint, endPoint);

      // Calculate rotation angle for the start point
      const rotationAngle = calculateRotationAngle(startPoint, endPoint);

      // Create updated fairway data with curved line
      const updatedFairwayData = {
        holeNumber: currentHole,
        teeLocation: {
          type: "Point",
          coordinates: startPoint,
        },
        basketLocation: {
          type: "Point",
          coordinates: endPoint,
        },
        fairwayPath: {
          type: "LineString",
          coordinates: curvedLine, // This now contains the curved line
        },
        length: distance,
        createdAt: new Date().toISOString(),
      };

      // Save the updated fairway (this will replace the existing one in localStorage)
      const success = saveFairway(updatedFairwayData);

      if (success) {
        // REPLACE the existing fairway in completedFairways array (don't add new one)
        setCompletedFairways((prev) => {
          // Find and replace the fairway with the same hole number
          return prev.map((fairway) =>
            fairway.holeNumber === currentHole
              ? {
                  holeNumber: currentHole,
                  startPoint: startPoint,
                  startPointRotation: rotationAngle, // Use calculated rotation angle
                  endPoint: endPoint,
                  fairwayLine: curvedLine, // Updated with curved line
                  createdAt: new Date().toISOString(),
                }
              : fairway
          );
        });
      }
    } catch (error) {
      console.error("Error saving curved fairway:", error);
    }
  };

  // Popup handlers
  const handleOpenPopup = () => {
    setIsPopupOpen(true);
  };

  const handleClosePopup = () => {
    setIsPopupOpen(false);
  };

  const handleZoomToFairway = (fairway) => {
    if (mapRef.current && mapRef.current.fitBounds) {
      // Calculate bounds for the fairway
      const bounds = new mapboxgl.LngLatBounds();
      bounds.extend(fairway.startPoint);
      bounds.extend(fairway.endPoint);

      // Add some padding and zoom to the fairway
      mapRef.current.fitBounds(bounds, {
        padding: 100,
        maxZoom: 16,
      });
    }
  };

  const handleDeleteFairway = (index) => {
    // Remove from completed fairways array
    setCompletedFairways((prev) => prev.filter((_, i) => i !== index));
    setCompletedWalkingPaths((prev) => prev.filter((_, i) => i !== index));

    // Update fairway count
    setFairwayCount((prev) => Math.max(0, prev - 1));
  };

  // Walking Path Popup handlers
  const handleOpenWalkingPathPopup = () => {
    setIsWalkingPathPopupOpen(true);
  };

  const handleCloseWalkingPathPopup = () => {
    setIsWalkingPathPopupOpen(false);
  };

  const handleZoomToWalkingPath = (path) => {
    if (mapRef.current && mapRef.current.fitBounds && path.walkingPath) {
      // Calculate bounds for the walking path
      const bounds = new mapboxgl.LngLatBounds();
      path.walkingPath.forEach((coord) => bounds.extend(coord));

      // Add some padding and zoom to the walking path
      mapRef.current.fitBounds(bounds, {
        padding: 100,
        maxZoom: 16,
      });
    }
  };

  const handleDeleteWalkingPath = (index) => {
    // Remove from completed walking paths array
    setCompletedWalkingPaths((prev) => prev.filter((_, i) => i !== index));
  };

  // Point editing handlers
  const handlePointClick = (pointType, position) => {
    setEditingPointType(pointType);
    setEditingPointPosition(position);
    setIsPointEditPopupOpen(true);
  };

  const handleEditPoint = () => {
    setIsPointEditPopupOpen(false);
    setCurrentMode(`editing-${editingPointType}`);
    setStatusMessage(`Click on map to move the ${editingPointType} point.`);
  };

  const handleClosePointEditPopup = () => {
    setIsPointEditPopupOpen(false);
    setEditingPointType(null);
    setEditingPointPosition(null);
  };

  // Curve editing handlers
  const handleCurvePointDrag = (pointIndex, newPosition) => {
    if (pointIndex === 1) {
      // Only middle point is draggable
      const updatedPoints = [...curveControlPoints];
      updatedPoints[pointIndex].position = newPosition;
      setCurveControlPoints(updatedPoints);

      // Create curved line using the control point
      const curvedLine = createCurvedLine(startPoint, newPosition, endPoint);
      setFairwayLine(curvedLine);
    }
  };

  // New function to handle movable curve control point dragging
  const handleMovableCurvePointDrag = (newPosition) => {
    if (!startPoint || !endPoint) return;

    // Calculate the midpoint between start and end points
    const midPoint = [
      startPoint[0] + (endPoint[0] - startPoint[0]) * 0.5,
      startPoint[1] + (endPoint[1] - startPoint[1]) * 0.5,
    ];

    // Calculate the direction vector from midpoint to the dragged position
    const dx = newPosition[0] - midPoint[0];
    const dy = newPosition[1] - midPoint[1];

    // Calculate the perpendicular direction to the start-end line
    const lineVectorX = endPoint[0] - startPoint[0];
    const lineVectorY = endPoint[1] - startPoint[1];
    const lineLength = Math.sqrt(
      lineVectorX * lineVectorX + lineVectorY * lineVectorY
    );

    // Normalize the line vector
    const normalizedLineX = lineVectorX / lineLength;
    const normalizedLineY = lineVectorY / lineLength;

    // Create perpendicular vector (rotate 90 degrees)
    const perpX = -normalizedLineY;
    const perpY = normalizedLineX;

    // Project the drag vector onto the perpendicular direction
    const projectionLength = dx * perpX + dy * perpY;

    // Create initial control point along the perpendicular direction
    let controlPoint = [
      midPoint[0] + perpX * projectionLength,
      midPoint[1] + perpY * projectionLength,
    ];

    // Iteratively adjust the control point to ensure equal segment distances
    for (let i = 0; i < 10; i++) {
      const dist1 = calculateDistance(startPoint, controlPoint);
      const dist2 = calculateDistance(controlPoint, endPoint);
      const totalDist = dist1 + dist2;
      const targetDist = totalDist / 2;

      // If distances are already close enough, break
      if (Math.abs(dist1 - dist2) < 0.5) break;

      // Adjust the position along the original line to balance the distances
      const adjustment = ((dist1 - targetDist) / totalDist) * 0.05;
      const adjustedMidPoint = [
        midPoint[0] - (endPoint[0] - startPoint[0]) * adjustment,
        midPoint[1] - (endPoint[1] - startPoint[1]) * adjustment,
      ];

      controlPoint = [
        adjustedMidPoint[0] + perpX * projectionLength,
        adjustedMidPoint[1] + perpY * projectionLength,
      ];
    }

    // Update the curve control point position
    setCurveControlPoint(controlPoint);

    // Create new curved line using the calculated control point
    const curvedLine = createCurvedLine(startPoint, controlPoint, endPoint);
    setFairwayLine(curvedLine);
  };

  const handleUndo = () => {
    if (walkingPath) {
      // Undo walking path
      setWalkingPath(null);
      setCurrentMode("fairway-saved");
      setStatusMessage(
        "Walking path removed. Click 'Walking Path' to create walking path."
      );
    } else if (previousFairwayLine && currentMode === "fairway-saved") {
      // Undo curve - restore previous fairway line (straight line)
      setFairwayLine(previousFairwayLine);
      setPreviousFairwayLine(null);

      // Save the straight line back to storage
      saveCurvedFairwayToStorage(previousFairwayLine);

      setStatusMessage(
        "Curve undone! Fairway restored to straight line. You can add curve, create walking path, or end fairway."
      );
    } else if (currentMode === "fairway-saved") {
      // If fairway is saved but user wants to undo, go back to fairway complete state
      setIsFairwaySaved(false);
      setWalkingPathStartPoint(null);
      setCurrentMode("fairway-complete");
      setStatusMessage(
        "Fairway is complete! Click 'End Fairway' to save it first."
      );
    } else if (currentMode === "fairway-complete" || endPoint) {
      setEndPoint(null);
      setFairwayLine(null);
      setPreviousFairwayLine(null); // Clear previous line when removing end point
      setCurrentMode("start-placed");
      setStatusMessage(
        "End point removed. Click 'Continue Fairway' to select end point."
      );
    } else if (startPoint) {
      setStartPoint(null);
      setStartPointRotation(0);
      // setIsRotatingStartPoint(false);
      setPreviousFairwayLine(null); // Clear previous line when removing start point
      setCurrentMode("idle");
      setStatusMessage(`Select start point to begin Fairway ${currentHole}`);
    }
  };

  const handleSaveCourse = async () => {
    try {
      setStatusMessage("Saving course to Supabase...");

      const result = await saveCourseToSupabase();

      if (result.success) {
        setStatusMessage(result.message);
        // Reset everything for a new course
        setStartPoint(null);
        setEndPoint(null);
        setFairwayLine(null);
        setWalkingPath(null);
        setCurrentHole(1);
        setCurrentMode("idle");

        setTimeout(() => {
          setStatusMessage("Course saved! Ready to create a new course.");
        }, 3000);
      } else {
        setStatusMessage(result.message);
      }
    } catch (error) {
      setStatusMessage("Error saving course to Supabase. Please try again.");
    }
  };

  // Mouse move handler for start point rotation
  const handleMouseMove = (coordinates) => {
    if (currentMode === "editing-curve") {
      // Keep existing curve editing functionality
      return;
    }
  };

  // Map click handler - this will be passed to MapView
  const handleMapClick = (coordinates) => {
    const [lng, lat] = coordinates;

    if (currentMode === "selecting-start") {
      // Place start point immediately
      setStartPoint([lng, lat]);
      setStartPointRotation(0); // Will be calculated when end point is placed
      setCurrentMode("start-placed");
      setStatusMessage("Start point placed! Click ‘Continue Fairway’.");
    } else if (currentMode === "selecting-end") {
      setEndPoint([lng, lat]);

      // Create fairway line
      const line = [startPoint, [lng, lat]];
      setFairwayLine(line);

      setCurrentMode("fairway-complete");
      setStatusMessage(
        "Fairway is complete! Click 'End Fairway' to save it first."
      );
    } else if (currentMode === "creating-walking-path") {
      // Only place the walking path endpoint, don't save automatically
      const walkingLine = [walkingPathStartPoint, [lng, lat]];
      setWalkingPath(walkingLine);
      setStatusMessage(
        "Walking path endpoint placed! Click 'Save Walking Path' to save."
      );
    } else if (currentMode === "editing-start") {
      // Update start point and recreate fairway line
      setStartPoint([lng, lat]);
      if (endPoint) {
        const line = [[lng, lat], endPoint];
        setFairwayLine(line);
      }
      setCurrentMode(endPoint ? "fairway-complete" : "start-placed");
      setStatusMessage(
        endPoint
          ? "Start point updated! Fairway is complete. Click 'End Fairway' to save it first."
          : "Start point updated! Click 'Continue Fairway' to select end point."
      );
    } else if (currentMode === "editing-end") {
      // Update end point and recreate fairway line
      setEndPoint([lng, lat]);
      const line = [startPoint, [lng, lat]];
      setFairwayLine(line);
      setCurrentMode("fairway-complete");
      setStatusMessage(
        "End point updated! Fairway is complete. Click 'End Fairway' to save it first."
      );
    } else if (currentMode === "selecting-curve-direction") {
      // Create curve based on click position
      createCurveFromClick([lng, lat]);
    }
  };

  return (
    <div style={{ position: "relative", height: "100vh", width: "100%" }}>
      <Header
        onOpenPopup={handleOpenPopup}
        onOpenWalkingPathPopup={handleOpenWalkingPathPopup}
      />
      <MapView
        ref={mapRef}
        onMapClick={handleMapClick}
        onMouseMove={handleMouseMove}
        onPointClick={handlePointClick}
        startPoint={startPoint}
        // startPointRotation={startPointRotation}
        // isRotatingStartPoint={isRotatingStartPoint}
        // rotationMode={rotationMode}
        endPoint={endPoint}
        fairwayLine={fairwayLine}
        walkingPath={walkingPath}
        completedFairways={completedFairways}
        completedWalkingPaths={completedWalkingPaths}
        currentMode={currentMode}
        curveControlPoints={curveControlPoints}
        onCurvePointDrag={handleCurvePointDrag}
        curveControlPoint={curveControlPoint}
        onMovableCurvePointDrag={handleMovableCurvePointDrag}
        originalStraightLine={originalStraightLine}
        // onStopRotation={handleStopRotation}
        // onStartClockwiseRotation={handleStartClockwiseRotation}
        // onStartAnticlockwiseRotation={handleStartAnticlockwiseRotation}
      />
      <ControlBox
        statusMessage={statusMessage}
        currentMode={currentMode}
        startPoint={startPoint}
        endPoint={endPoint}
        fairwayLine={fairwayLine}
        walkingPath={walkingPath}
        handleContinueFairway={handleContinueFairway}
        handleStartPoint={handleStartPoint}
        handleEndPoint={handleEndPoint}
        handleSaveFairway={handleSaveFairway}
        handleCreateWalkingPath={handleCreateWalkingPath}
        handleUndo={handleUndo}
        handleSaveCourse={handleSaveCourse}
        handleAddCurve={handleAddCurve}
        fairwayCount={fairwayCount}
        isFairwaySaved={isFairwaySaved}
        walkingPathStartPoint={walkingPathStartPoint}
        handleSaveWalkingPath={handleSaveWalkingPath}
        handleSaveCurve={handleSaveCurve}
        handleCancelCurve={handleCancelCurve}
        handleEndFairway={handleEndFairway}
      />

      {/* SavedFairway Popup */}
      <SavedFairway
        isOpen={isPopupOpen}
        onClose={handleClosePopup}
        completedFairways={completedFairways}
        onZoomToFairway={handleZoomToFairway}
        onDeleteFairway={handleDeleteFairway}
      />

      {/* SavedWalkingPath Popup */}
      <SavedWalkingPath
        isOpen={isWalkingPathPopupOpen}
        onClose={handleCloseWalkingPathPopup}
        completedWalkingPaths={completedWalkingPaths}
        onZoomToWalkingPath={handleZoomToWalkingPath}
        onDeleteWalkingPath={handleDeleteWalkingPath}
      />

      {/* Point Edit Popup */}
      {isPointEditPopupOpen && (
        <div
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            zIndex: 2000,
          }}
          onClick={handleClosePointEditPopup}
        >
          <div
            style={{
              backgroundColor: "white",
              borderRadius: "12px",
              padding: "24px",
              boxShadow: "0 8px 32px rgba(0,0,0,0.12)",
              minWidth: "300px",
              textAlign: "center",
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <h3
              style={{
                margin: "0 0 16px 0",
                fontSize: "18px",
                fontWeight: "600",
              }}
            >
              Fairway {editingPointType === "start" ? "Start" : "End"} Point
            </h3>
            <p
              style={{ margin: "0 0 20px 0", color: "#666", fontSize: "14px" }}
            >
              Tap to edit
            </p>
            <div
              style={{ display: "flex", gap: "12px", justifyContent: "center" }}
            >
              <button
                onClick={handleEditPoint}
                style={{
                  backgroundColor: "#2563eb",
                  color: "white",
                  border: "none",
                  borderRadius: "6px",
                  padding: "10px 20px",
                  cursor: "pointer",
                  fontSize: "14px",
                  fontWeight: "500",
                }}
              >
                Edit Point
              </button>
              <button
                onClick={handleClosePointEditPopup}
                style={{
                  backgroundColor: "#e5e7eb",
                  color: "#374151",
                  border: "none",
                  borderRadius: "6px",
                  padding: "10px 20px",
                  cursor: "pointer",
                  fontSize: "14px",
                  fontWeight: "500",
                }}
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Home;
